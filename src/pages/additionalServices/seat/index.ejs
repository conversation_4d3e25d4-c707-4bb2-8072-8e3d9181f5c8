<%
  // Mock data for flight segments and seat selection
  const flightSegments = [
    {
      departureCity: 'Beijing',
      arrivalCity: 'Shanghai',
      flightDate: '2025-08-15',
      departureTime: '08:30',
      arrivalTime: '11:15',
      status: 'selected',
      changesInfo: 'Free changes within 24 hours, cancellation fee applies after 24 hours',
      passengers: [
        {name: 'ZHANG/SAN', badge: '终身白金卡', id: '1230000213', selectedSeat: '10E', currency: '', amount: ''},
        {name: 'LI/SAN', selectedSeat: '10K'},
        {name: 'WANG/SAN'},
        {name: 'ZHAO/SAN'}
      ]
    },
    {
      departureCity: 'Shanghai',
      arrivalCity: 'Guangzhou',
      flightDate: '2025-08-16',
      departureTime: '14:20',
      arrivalTime: '17:05',
      status: 'no-select',
      changesInfo: 'Changes allowed up to 2 hours before departure, cancellation fee: ¥200',
      passengers: [
        {name: 'ZHANG/SAN', badge: '终身白金卡', id: '1230000213', selectedSeat: '13E'},
        {name: 'LI/SAN', selectedSeat: '13K'},
        {name: 'WANG/SAN'},
        {name: 'ZHAO/SAN'}
      ]
    },
    {
      departureCity: 'Guangzhou',
      arrivalCity: 'Shenzhen',
      flightDate: '2025-08-17',
      departureTime: '19:45',
      arrivalTime: '20:30',
      status: 'no-select',
      changesInfo: 'No changes allowed, full refund available within 24 hours',
      passengers: [
        {name: 'ZHANG/SAN', badge: '终身白金卡', id: '1230000213', selectedSeat: ''},
        {name: 'LI/SAN', selectedSeat: ''},
        {name: 'WANG/SAN'},
        {name: 'ZHAO/SAN'}
      ]
    }
  ];

  // Flight status mapping
  const statusMapping = {
    'no-select':{},
    selected: { icon: 'check.svg', text: 'Selected', class: 'status-selected' }
  };
%>

<div>
  <div class="select-seat-container">
    <% flightSegments.forEach((segment, index) => { %>
    <div class="seat-section" id="seat-section-<%= index %>">
      <div class="wrap-expand-card">
        <%- include('../expand-card/index', { contentId: 'seat-content-' + index, flightData: segment, statusInfo:
        statusMapping[segment.status], index: index }) %>
      </div>
      <div id="seat-content-<%= index %>" style="display: none">
        <%- include('./content', { passengers: segment.passengers }) %>
      </div>
    </div>
    <% }) %>
  </div>

  <div class="boarding-services">
    <div class="boarding-services-title">Boarding services:</div>
    <div class="boarding-services-text">
      Gentle hint:Select your seat and confirm. The system will automatically reserve check-in services for you after
      the ticket is issued. The check-in results will be sent to you via SMS and email after flight initialization.
    </div>
  </div>

  <!-- Button container with Skip and Confirm buttons -->
  <div class="button-container">
    <button class="btn-skip" type="button">Skip this step</button>
    <button class="btn-confirm" type="button">Confirm</button>
  </div>
</div>
