<%

const _passengers = typeof passengers !== 'undefined' ? passengers: undefined;


%>

<div class="seat-content">
  <!-- Notes Section -->
  <div class="seat-notes">
    <div class="notes-content">
      <div class="notes-title">Notes:</div>
      <div class="notes-text">
        You have not selected seats for all passengers yet, which may result in splitting during check-in!
        <br />
        After clicking the "Book" button to successfully book, the seat selection information for this flight segment
        cannot be modified.
      </div>
    </div>
  </div>

  <!-- Seat Selection Legend and Passenger Panel -->
  <div class="seat-selection-container">
    <!-- Seat Legend Panel -->
    <div class="seat-legend-panel">
      <!-- Legend Items -->
      <div class="seat-legend">
        <div class="legend-row">
          <div class="legend-item">
            <div class="seat-icon seat-free"></div>
            <span class="legend-text">Free seats</span>
          </div>
          <div class="legend-item">
            <div class="seat-icon seat-unreserved"></div>
            <span class="legend-text">Unreserved</span>
          </div>
          <div class="legend-item">
            <div class="seat-icon seat-not-available">
              <div class="lock-icon"></div>
            </div>
            <span class="legend-text">Not Available</span>
          </div>
          <div class="legend-item">
            <div class="seat-icon seat-emergency"></div>
            <span class="legend-text">Emergency exit</span>
          </div>
        </div>
        <div class="legend-row">
          <div class="legend-item">
            <div class="seat-icon seat-unadjustable">
              <div class="star-icon"></div>
            </div>
            <span class="legend-text">Unadjustable seat</span>
          </div>
          <div class="legend-item">
            <div class="seat-icon seat-paid"></div>
            <span class="legend-text">Paid seat</span>
          </div>
          <div class="legend-item">
            <div class="seat-icon seat-selected"></div>
            <span class="legend-text">Selected</span>
          </div>
          <div class="legend-item"></div>
        </div>
      </div>

      <!-- Aircraft Seat Map -->
      <%- include('./aircraft-model') %>
    </div>

    <!-- Passenger Selection Panel -->
    <div class="passenger-panel">
      <div class="panel-header">
        <h3 class="panel-title">Select passenger</h3>
      </div>

      <div class="passenger-list">
        <% if (_passengers && _passengers.length > 0) { %> <% _passengers.forEach((passenger, passengerIndex) => { %>
        <div class="passenger-item" data-passenger-index="<%= passengerIndex %>">
          <div class="passenger-number"><%= passengerIndex + 1 %></div>
          <div class="passenger-content">
            <div class="passenger-info">
              <div class="passenger-name"><%= passenger.name %></div>
              <div class="passenger-details">
                <% if (passenger.badge) { %>
                <div class="passenger-badge"><%= passenger.badge %></div>
                <% } %>
                <% if (passenger.id) { %>
                <div class="passenger-id"><%= passenger.id %></div>
                <% } %>
              </div>
            </div>
            <div class="seat-info">
              <% if (passenger.selectedSeat) { %>
              <div class="seat-display">
                <div class="seat-icon-small"></div>
                <div class="seat-number"><%= passenger.selectedSeat %></div>
              </div>

              <div class="seat-price"><%= passenger.currency %> <%= passenger.amount %></div>
              <% } else { %>
              <div class="no-seat-selected">No seat selected</div>
              <% } %>
            </div>
          </div>
        </div>
        <% }) %>
        <% } else { %>
        <div class="no-passengers">No passengers found</div>
        <% } %>
      </div>
    </div>
  </div>
</div>
