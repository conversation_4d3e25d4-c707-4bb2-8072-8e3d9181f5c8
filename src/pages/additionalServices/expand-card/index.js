/**
 * 航班详情展开/收起功能
 */
$(document).ready(function () {
  // 使用事件委托处理多个expand-card的点击事件
  $(document).on('click', '.expand-control', function () {
    const $element = $(this);
    const $expandIcon = $element.find('.expand-icon');
    const $expandText = $element.find('.expand-text');
    const $flightCard = $element.closest('.flight-info-card');
    const $expandContent = $flightCard.find('.expand-content');

    // 切换展开状态
    const isExpanded = $flightCard.hasClass('expanded');

    if (isExpanded) {
      // 收起
      $flightCard.removeClass('expanded');
      $expandText.text('expand');
      $expandIcon.css('transform', 'rotate(0deg)');
      $expandContent.hide();
    } else {
      // 展开
      $flightCard.addClass('expanded');
      $expandText.text('Retract');
      $expandIcon.css('transform', 'rotate(-180deg)');

      // 查找并渲染对应的源内容
      const sourceContentId = $expandContent.data('source');
      if (sourceContentId) {
        const $sourceContent = $('#' + sourceContentId);
        if ($sourceContent.length > 0) {
          $expandContent.html($sourceContent.html());
        }
      }

      $expandContent.show();
    }
  });
});
